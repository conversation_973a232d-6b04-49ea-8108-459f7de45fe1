import { Document, DocumentData } from "./Document";
import { VectorService, VectorData } from "../core/VectorService";
import { logger } from "../config/logger";
import Storage from "../storage/Storage";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { FileStream } from "../storage/FileStream";
import mammoth from "mammoth";
import pdfParse from "pdf-parse";
import csv from "csv-parser";
import xlsx from "xlsx";
import { Readable } from "stream";
import {
  OnboardingJobTracker,
  OnboardingJob,
} from "../locations/OnboardingJobTracker";
import DocumentVectorJob from "./DocumentVectorJob";

// Constants for the service
const DOCUMENT_INDEX = "document-embeddings";
const CHUNK_SIZE = 1000; // Characters per chunk
const CHUNK_OVERLAP = 200; // Characters of overlap between chunks
const MAX_BATCH_SIZE = 5; // Process 5 chunks at a time to prevent memory overload
const MEMORY_CLEANUP_INTERVAL = 10; // Force GC every 10 chunks
const PROCESSING_DELAY = 100; // 100ms delay between batches to prevent overwhelming

export interface DocumentChunk {
  id: string;
  content: string;
  metadata: {
    document_id: number;
    location_id: number;
    chunk_index: number;
    total_chunks: number;
    page?: number;
    document_name: string;
    document_type: string;
    created_at: number;
  };
}

export class DocumentVectorService {
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  /**
   * Initialize the vector service and ensure the document index exists
   */
  static async initialize(): Promise<boolean> {
    try {
      await this.getVectorService().initialize();
      await this.ensureIndexExists();
      logger.info("DocumentVectorService initialized successfully");
      return true;
    } catch (error) {
      logger.error("Failed to initialize DocumentVectorService:", error);
      throw error;
    }
  }

  /**
   * Processes a document and adds it to the vector database
   */
  static async processDocument(
    documentId: number,
    locationId: number,
    file?: FileStream
  ): Promise<boolean> {
    try {
      logger.info(
        `Processing document ${documentId} for location ${locationId}`
      );

      // Get the document record from the database
      const document = await Document.first((qb) =>
        qb.where({ id: documentId, location_id: locationId })
      );

      if (!document) {
        throw new Error(
          `Document ${documentId} not found for location ${locationId}`
        );
      }

      // Download the document file if not provided
      let tempFilePath: string | null = null;
      let contentBuffer: Buffer | null = null;

      if (!file && document.storage_path) {
        try {
          logger.info(
            `Downloading document from storage: ${document.storage_path}`
          );

          // Get the public URL for the file
          const publicUrl = Storage.url(document.storage_path);

          // Fetch the file content
          const response = await fetch(publicUrl);
          if (!response.ok) {
            throw new Error(
              `Failed to download file from storage: ${response.statusText}`
            );
          }

          contentBuffer = Buffer.from(await response.arrayBuffer());

          // Create a temporary file to work with
          const tempDir = path.join(os.tmpdir(), "document-processor");
          fs.mkdirSync(tempDir, { recursive: true });
          tempFilePath = path.join(tempDir, document.name);
          fs.writeFileSync(tempFilePath, contentBuffer);

          logger.info(
            `Document ${documentId} downloaded to temporary file: ${tempFilePath}`
          );
        } catch (downloadError) {
          logger.error(
            `Error downloading document ${documentId}:`,
            downloadError
          );
          throw new Error(
            `Failed to download document: ${
              downloadError instanceof Error
                ? downloadError.message
                : String(downloadError)
            }`
          );
        }
      } else if (file) {
        try {
          // Convert FileStream to buffer and temporary file
          const chunks: Buffer[] = [];
          for await (const chunk of file.file as any) {
            chunks.push(Buffer.from(chunk));
          }
          contentBuffer = Buffer.concat(chunks);

          // Create a temporary file
          const tempDir = path.join(os.tmpdir(), "document-processor");
          fs.mkdirSync(tempDir, { recursive: true });
          tempFilePath = path.join(tempDir, file.metadata.fileName);
          fs.writeFileSync(tempFilePath, contentBuffer);

          logger.info(
            `Document ${documentId} from stream saved to temporary file: ${tempFilePath}`
          );
        } catch (fileError) {
          logger.error(
            `Error processing file stream for document ${documentId}:`,
            fileError
          );
          throw new Error(
            `Failed to process file stream: ${
              fileError instanceof Error ? fileError.message : String(fileError)
            }`
          );
        }
      } else {
        throw new Error(
          `No file or storage path available for document ${documentId}`
        );
      }

      // Extract content based on file type
      const fileName = document.name;
      const fileType = document.type || this.getMimeTypeFromFileName(fileName);

      logger.info(`Extracting content from ${fileName} (type: ${fileType})`);

      let extractedContent: string;
      try {
        extractedContent = await this.extractContent(
          tempFilePath!,
          fileName,
          fileType
        );
      } catch (extractError) {
        logger.error(
          `Error extracting content from ${fileName}:`,
          extractError
        );
        throw new Error(
          `Content extraction failed: ${
            extractError instanceof Error
              ? extractError.message
              : String(extractError)
          }`
        );
      }

      // Clean up temporary file and force garbage collection
      if (tempFilePath) {
        try {
          fs.unlinkSync(tempFilePath);
          logger.debug(`Deleted temporary file: ${tempFilePath}`);
        } catch (unlinkError) {
          logger.warn(
            `Failed to delete temporary file ${tempFilePath}:`,
            unlinkError
          );
        }
      }

      // Clear content buffer from memory and force garbage collection
      contentBuffer = null;
      if (global.gc) {
        global.gc();
        logger.debug(`Forced garbage collection after content extraction`);
      }

      // 🛡️ SAFETY CHECK: Implement circuit breaker for very large documents
      const maxReasonableChunks = 100; // ~100KB of text content (100 * 1000 chars)
      const estimatedChunks = Math.ceil(extractedContent.length / CHUNK_SIZE);

      if (estimatedChunks > maxReasonableChunks) {
        logger.warn(
          `Document ${documentId} is very large (${estimatedChunks} estimated chunks). ` +
            `Truncating to first ${
              maxReasonableChunks * CHUNK_SIZE
            } characters to prevent memory issues.`
        );
        extractedContent = extractedContent.substring(
          0,
          maxReasonableChunks * CHUNK_SIZE
        );
      }

      // Create chunks from the extracted content
      const chunks = this.createChunks(
        extractedContent,
        documentId,
        locationId,
        document.name,
        document.type
      );

      if (chunks.length === 0) {
        logger.warn(`No content chunks created for document ${documentId}`);

        // Update document with completed status (no chunks to vectorize)
        const currentDocument = await Document.first((qb) =>
          qb.where({ id: documentId })
        );

        // Parse existing data if it's a string
        let existingData = {};
        if (currentDocument?.data) {
          if (typeof currentDocument.data === "string") {
            try {
              existingData = JSON.parse(currentDocument.data);
            } catch (e) {
              logger.warn(
                `Failed to parse existing document data for ${documentId}: ${e}`
              );
              existingData = {};
            }
          } else {
            existingData = currentDocument.data;
          }
        }

        await Document.update((qb) => qb.where({ id: documentId }), {
          pinecone_file_id: `internal_${documentId}`,
          data: {
            ...existingData,
            vectorization: {
              status: "completed",
              chunks_total: 0,
              chunks_indexed: 0,
              chunks_failed: 0,
              completed_at: new Date().toISOString(),
            },
          },
        });

        logger.info(
          `Document ${documentId} vectorization completed with no chunks (likely scanned document)`
        );

        // Also log completion info for job tracking
        logger.info(
          `Completing document_vectorization job for location ${locationId}`
        );
        return true; // Consider this a successful completion
      }

      logger.info(
        `Created ${chunks.length} content chunks for document ${documentId}`
      );

      // Use namespace pattern for location-specific data
      const namespace = this.getNamespace(locationId);

      // 🚀 FRIENDLY STREAMING VECTORIZATION - Process chunks in small batches
      logger.info(
        `Starting friendly streaming vectorization for ${chunks.length} chunks`
      );

      let processedCount = 0;
      let totalVectorsUpserted = 0;
      let totalErrorCount = 0;

      // Process chunks in small batches to prevent memory overload
      for (let i = 0; i < chunks.length; i += MAX_BATCH_SIZE) {
        const batchChunks = chunks.slice(i, i + MAX_BATCH_SIZE);

        logger.info(
          `Processing batch ${Math.ceil(
            (i + 1) / MAX_BATCH_SIZE
          )} of ${Math.ceil(chunks.length / MAX_BATCH_SIZE)} (${
            batchChunks.length
          } chunks)`
        );

        // Prepare vector data for this batch
        const batchVectorData = batchChunks.map(
          (chunk): VectorData => ({
            id: chunk.id,
            text: chunk.content,
            metadata: {
              ...chunk.metadata,
              document_id: documentId,
              location_id: locationId,
              source_type: "document",
              source_id: documentId.toString(),
              content: chunk.content,
              created_at: Date.now(),
              updated_at: Date.now(),
            },
          })
        );

        try {
          // Process this batch with smaller size for memory efficiency
          const batchResult = await this.getVectorService().upsertVectors(
            DOCUMENT_INDEX,
            batchVectorData,
            batchChunks.length, // Use actual batch size
            namespace
          );

          totalVectorsUpserted +=
            batchResult.successCount || batchChunks.length;
          totalErrorCount += batchResult.errorCount || 0;
          processedCount += batchChunks.length;

          logger.info(
            `Batch completed: ${processedCount}/${chunks.length} chunks processed`
          );

          // 🧹 Memory-friendly cleanup every MEMORY_CLEANUP_INTERVAL chunks
          if (processedCount % MEMORY_CLEANUP_INTERVAL === 0 && global.gc) {
            global.gc();
            logger.debug(
              `Memory cleanup performed after ${processedCount} chunks`
            );
          }

          // 😴 Brief pause to prevent overwhelming the system
          if (i + MAX_BATCH_SIZE < chunks.length) {
            await new Promise((resolve) =>
              setTimeout(resolve, PROCESSING_DELAY)
            );
          }
        } catch (error) {
          logger.error(
            `Error processing batch ${Math.ceil(
              (i + 1) / MAX_BATCH_SIZE
            )}: ${error}`
          );
          throw error;
        }
      }

      logger.info(
        `🎉 Friendly vectorization completed! Processed ${processedCount} chunks, upserted ${totalVectorsUpserted} vectors`
      );

      const result = {
        successCount: totalVectorsUpserted,
        errorCount: totalErrorCount,
        vectorsUpserted: totalVectorsUpserted,
      };

      logger.info(
        `Document ${documentId} indexing complete: ${totalVectorsUpserted} vectors indexed successfully`
      );

      // Force garbage collection after vectorization
      if (global.gc) {
        global.gc();
        logger.debug(`Forced garbage collection after vectorization`);
      }

      // Update document record with vectorization status
      // First, ensure we get the latest document data
      const currentDocument = await Document.first((qb) =>
        qb.where({ id: documentId })
      );

      // Parse existing data if it's a string
      let existingData = {};
      if (currentDocument?.data) {
        if (typeof currentDocument.data === "string") {
          try {
            existingData = JSON.parse(currentDocument.data);
          } catch (e) {
            logger.warn(
              `Failed to parse existing document data for ${documentId}: ${e}`
            );
            existingData = {};
          }
        } else {
          existingData = currentDocument.data;
        }
      }

      await Document.update((qb) => qb.where({ id: documentId }), {
        pinecone_file_id: `internal_${documentId}`,
        data: {
          ...existingData,
          vectorization: {
            status: result.errorCount === 0 ? "completed" : "partial",
            chunks_total: chunks.length,
            chunks_indexed: result.successCount,
            chunks_failed: result.errorCount,
            completed_at: new Date().toISOString(),
          },
        },
      });

      return result.successCount > 0;
    } catch (error) {
      logger.error(`Error processing document ${documentId}:`, error);

      // Update document with error status
      try {
        // Get current document data
        const currentDocument = await Document.first((qb) =>
          qb.where({ id: documentId })
        );

        // Parse existing data if it's a string
        let existingData = {};
        if (currentDocument?.data) {
          if (typeof currentDocument.data === "string") {
            try {
              existingData = JSON.parse(currentDocument.data);
            } catch (e) {
              logger.warn(
                `Failed to parse existing document data for ${documentId}: ${e}`
              );
              existingData = {};
            }
          } else {
            existingData = currentDocument.data;
          }
        }

        await Document.update((qb) => qb.where({ id: documentId }), {
          data: {
            ...existingData,
            vectorization_error:
              error instanceof Error ? error.message : String(error),
            vectorization_error_time: new Date().toISOString(),
          },
        });
      } catch (updateError) {
        logger.error(
          `Failed to update document ${documentId} status after error:`,
          updateError
        );
      }

      throw error;
    }
  }

  /**
   * Extract content from a file based on its type
   */
  private static async extractContent(
    filePath: string,
    fileName: string,
    fileType: string
  ): Promise<string> {
    // Determine file type from MIME type or file extension if not provided
    const mimeType = fileType || this.getMimeTypeFromFileName(fileName);
    const extension = path.extname(fileName).toLowerCase();

    logger.info(
      `Extracting content from ${fileName}, MIME: ${mimeType}, Extension: ${extension}`
    );

    if (!mimeType || mimeType === "undefined") {
      // If mimeType is undefined, try to infer from filename
      const inferredMimeType = this.getMimeTypeFromFileName(fileName);
      if (inferredMimeType && inferredMimeType !== "application/octet-stream") {
        logger.info(
          `Inferred MIME type ${inferredMimeType} from filename ${fileName}`
        );
        return await this.extractContent(filePath, fileName, inferredMimeType);
      }
      logger.warn(
        `Unable to determine file type for: ${fileName} (MIME: ${mimeType})`
      );
      return `Content from file ${fileName} could not be extracted due to unknown file type.`;
    }

    if (mimeType.includes("pdf") || extension === ".pdf") {
      return await this.extractFromPdf(filePath);
    } else if (
      mimeType.includes("word") ||
      [".docx", ".doc"].includes(extension)
    ) {
      return await this.extractFromWord(filePath);
    } else if (
      mimeType.includes("text") ||
      extension === ".txt" ||
      extension === ".md"
    ) {
      return await this.extractFromText(filePath);
    } else if (mimeType.includes("csv") || extension === ".csv") {
      return await this.extractFromCsv(filePath);
    } else if (
      mimeType.includes("spreadsheet") ||
      [".xlsx", ".xls"].includes(extension)
    ) {
      return await this.extractFromExcel(filePath);
    } else {
      // Default to treating as text for unknown types
      try {
        return await this.extractFromText(filePath);
      } catch {
        return `Content from file ${fileName} could not be extracted due to unsupported format.`;
      }
    }
  }

  /**
   * Extract content from a PDF file
   */
  private static async extractFromPdf(filePath: string): Promise<string> {
    const dataBuffer = fs.readFileSync(filePath);

    // Check file size to prevent memory issues - limit to 5MB for better memory safety
    const MAX_FILE_SIZE_MB = 5;
    if (dataBuffer.length > MAX_FILE_SIZE_MB * 1024 * 1024) {
      logger.warn(
        `PDF file too large (${(dataBuffer.length / (1024 * 1024)).toFixed(
          2
        )}MB), using fallback extraction`
      );
      return `Content from file ${path.basename(
        filePath
      )}. This file was too large for detailed extraction.`;
    }

    try {
      // Use pdf-parse for better text extraction
      const data = await pdfParse(dataBuffer);
      const extractedText = data.text;

      logger.info(
        `PDF text extraction completed. Length: ${extractedText.length} characters`
      );

      // If no text was extracted, it might be a scanned PDF
      if (!extractedText || extractedText.trim().length === 0) {
        logger.warn(
          `No text extracted from PDF ${path.basename(
            filePath
          )} - likely a scanned document`
        );
        return `Content from PDF document ${path.basename(
          filePath
        )}. This appears to be a scanned document with no extractable text layer.`;
      } else {
        return extractedText;
      }
    } catch (error) {
      logger.error(`Error parsing PDF: ${error}`);

      // Try fallback approach for corrupted PDFs - extract whatever text we can
      try {
        logger.info(
          `Using fallback content extraction for ${path.basename(filePath)}`
        );

        // Return a placeholder message with the file name to allow processing to continue
        return `Content from file ${path.basename(
          filePath
        )}. This file appears to be corrupted or in an unsupported format. Processing with limited text extraction.`;
      } catch (fallbackError) {
        logger.error(`Fallback extraction also failed: ${fallbackError}`);
        throw new Error(
          `PDF extraction failed: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    } finally {
      // Force garbage collection after PDF processing to free memory
      if (global.gc) {
        global.gc();
        logger.debug(`Forced garbage collection after PDF processing`);
      }
    }
  }

  /**
   * Extract content from a Word document
   */
  private static async extractFromWord(filePath: string): Promise<string> {
    const buffer = fs.readFileSync(filePath);
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  }

  /**
   * Extract content from a plain text file
   */
  private static async extractFromText(filePath: string): Promise<string> {
    return fs.readFileSync(filePath, "utf8");
  }

  /**
   * Extract content from a CSV file
   */
  private static async extractFromCsv(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const rows: Record<string, string>[] = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data: Record<string, string>) => rows.push(data))
        .on("end", () => {
          if (rows.length === 0) {
            resolve("No data found in CSV file");
            return;
          }

          const headers = Object.keys(rows[0] || {});
          const content = [
            headers.join("\t"),
            ...rows.map((row) =>
              headers.map((header) => row[header]).join("\t")
            ),
          ].join("\n");
          resolve(content);
        })
        .on("error", (error: Error) => reject(error));
    });
  }

  /**
   * Extract content from an Excel file
   */
  private static async extractFromExcel(filePath: string): Promise<string> {
    const workbook = xlsx.readFile(filePath);
    const result: string[] = [];

    workbook.SheetNames.forEach((sheetName: string) => {
      const worksheet = workbook.Sheets[sheetName];
      const sheetData = xlsx.utils.sheet_to_json(worksheet);

      if (sheetData.length > 0) {
        // Add sheet header
        result.push(`--- Sheet: ${sheetName} ---`);

        // Cast the first row to Record<string, unknown> to avoid type errors
        const firstRow = sheetData[0] as Record<string, unknown>;
        const headers = Object.keys(firstRow);
        result.push(headers.join("\t"));

        // Add rows with proper type casting
        sheetData.forEach((row) => {
          const typedRow = row as Record<string, unknown>;
          result.push(
            headers.map((header) => String(typedRow[header] || "")).join("\t")
          );
        });

        result.push(""); // Add empty line between sheets
      }
    });

    return result.join("\n");
  }

  /**
   * Create chunks from content with proper overlap
   */
  private static createChunks(
    content: string,
    documentId: number,
    locationId: number,
    documentName: string,
    documentType: string
  ): DocumentChunk[] {
    if (!content || content.trim().length === 0) {
      return [];
    }

    const chunks: DocumentChunk[] = [];
    let startIndex = 0;
    let chunkIndex = 0;

    while (startIndex < content.length) {
      const endIndex = Math.min(startIndex + CHUNK_SIZE, content.length);
      const chunkContent = content.substring(startIndex, endIndex);

      if (chunkContent.trim().length === 0) {
        startIndex = endIndex - CHUNK_OVERLAP;
        continue;
      }

      chunks.push({
        id: `doc_${documentId}_chunk_${chunkIndex}`,
        content: chunkContent,
        metadata: {
          document_id: documentId,
          location_id: locationId,
          chunk_index: chunkIndex,
          total_chunks: 0, // Will be updated later
          document_name: documentName,
          document_type: documentType,
          created_at: Date.now(),
        },
      });

      chunkIndex++;
      startIndex = endIndex - CHUNK_OVERLAP;
    }

    // Update total_chunks field
    chunks.forEach((chunk) => {
      chunk.metadata.total_chunks = chunks.length;
    });

    return chunks;
  }

  /**
   * Search document chunks based on a query
   */
  static async searchDocumentChunks(
    query: string,
    locationId: number,
    limit: number = 5
  ): Promise<DocumentChunk[]> {
    try {
      if (!query) {
        throw new Error("Empty query provided to document search");
      }

      // Get namespace for this location
      const namespace = this.getNamespace(locationId);

      const results = await this.getVectorService().queryVectors(
        DOCUMENT_INDEX,
        query,
        { source_type: "document" }, // Only search document vectors
        limit,
        namespace
      );

      // Convert VectorQueryResult to DocumentChunk - note that VectorQueryResult has 'metadata' but not 'text'
      return results.map((result) => ({
        id: result.id,
        content: result.metadata.content || result.metadata.text || "", // Try to get content from metadata
        metadata: {
          document_id: Number(result.metadata.document_id),
          location_id: Number(result.metadata.location_id),
          chunk_index: Number(result.metadata.chunk_index || 0),
          total_chunks: Number(result.metadata.total_chunks || 1),
          document_name: String(result.metadata.document_name || "Unknown"),
          document_type: String(result.metadata.document_type || "Unknown"),
          created_at: Number(result.metadata.created_at || Date.now()),
        },
      }));
    } catch (error) {
      logger.error({
        message: "Error searching document vectors",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        query,
        location_id: locationId,
      });
      return [];
    }
  }

  /**
   * Delete document vectors
   */
  static async deleteDocumentVectors(
    documentId: number,
    locationId: number
  ): Promise<boolean> {
    try {
      const namespace = this.getNamespace(locationId);

      // Use a custom filter to delete by document_id
      await this.getVectorService().deleteNamespace(DOCUMENT_INDEX, namespace);

      logger.info(
        `Deleted vectors for document ${documentId} in location ${locationId}`
      );
      return true;
    } catch (error) {
      logger.error({
        message: "Error deleting document vectors",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        document_id: documentId,
        location_id: locationId,
      });
      return false;
    }
  }

  /**
   * Reindex all documents for a location
   * This starts a background process to reindex all documents for a location
   */
  static async reindexLocationDocuments(
    locationId: number
  ): Promise<{ jobId: string; documentCount: number }> {
    try {
      logger.info(
        `Starting reindex of all documents for location ${locationId}`
      );

      // Get all documents for the location
      const documents = await Document.query()
        .where({ location_id: locationId })
        .whereNull("deleted_at")
        .orderBy("created_at", "desc");

      if (!documents || documents.length === 0) {
        logger.info(`No documents found for location ${locationId}`);
        return { jobId: "none", documentCount: 0 };
      }

      logger.info(
        `Found ${documents.length} documents to reindex for location ${locationId}`
      );

      // Create a job tracker entry
      const jobId = `reindex-loc-${locationId}-${Date.now()}`;
      const jobType = "document_vectorization";

      try {
        // Track as an onboarding job for status reporting
        OnboardingJobTracker.startJob(locationId, jobType);
      } catch (trackError) {
        logger.warn(`Could not create job tracker: ${trackError}`);
        // Continue anyway
      }

      // Queue jobs for each document
      let queuedCount = 0;
      for (const document of documents) {
        try {
          // Create and queue the job
          const job = await DocumentVectorJob.from({
            document_id: document.id,
            location_id: locationId,
          }).queue();

          queuedCount++;
        } catch (queueError) {
          logger.error(
            `Error queueing reindex job for document ${document.id}: ${queueError}`
          );
          // Continue with other documents
        }
      }

      logger.info(
        `Queued ${queuedCount} document vectorization jobs for location ${locationId}`
      );

      return {
        jobId,
        documentCount: documents.length,
      };
    } catch (error) {
      logger.error({
        message: "Error starting document reindexing",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        location_id: locationId,
      });
      throw error;
    }
  }

  /**
   * Get indexing status for a location
   * Returns information about document indexing progress
   */
  static async getLocationIndexingStatus(locationId: number): Promise<{
    total_documents: number;
    indexed_documents: number;
    failed_documents: number;
    in_progress_documents: number;
    status: "not_started" | "in_progress" | "completed" | "failed";
    last_updated: string;
  }> {
    try {
      logger.info(
        `Getting document indexing status for location ${locationId}`
      );

      // Get all documents for the location
      const documents = await Document.query()
        .where({ location_id: locationId })
        .whereNull("deleted_at");

      if (!documents || documents.length === 0) {
        return {
          total_documents: 0,
          indexed_documents: 0,
          failed_documents: 0,
          in_progress_documents: 0,
          status: "not_started",
          last_updated: new Date().toISOString(),
        };
      }

      // Count documents in different states
      let indexedCount = 0;
      let failedCount = 0;
      let inProgressCount = 0;
      let lastUpdated = new Date(0).toISOString();

      for (const doc of documents) {
        // Check if document has been vectorized by looking at document data
        const docData =
          typeof doc.data === "string"
            ? JSON.parse(doc.data || "{}")
            : doc.data || {};

        const vectorization = docData.vectorization || {};

        if (vectorization.status === "completed") {
          indexedCount++;
        } else if (
          vectorization.status === "failed" ||
          docData.vectorization_error
        ) {
          failedCount++;
        } else if (doc.status === "processing" || doc.status === "pending") {
          inProgressCount++;
        }

        // Track the most recent update
        const completedAt = vectorization.completed_at;
        const errorTime = docData.vectorization_error_time;

        if (completedAt && completedAt > lastUpdated) {
          lastUpdated = completedAt;
        }

        if (errorTime && errorTime > lastUpdated) {
          lastUpdated = errorTime;
        }
      }

      // Determine overall status
      let status: "not_started" | "in_progress" | "completed" | "failed";

      if (indexedCount === documents.length) {
        status = "completed";
      } else if (indexedCount === 0 && failedCount === documents.length) {
        status = "failed";
      } else if (inProgressCount > 0 || indexedCount > 0) {
        status = "in_progress";
      } else {
        status = "not_started";
      }

      // Check onboarding job tracker for more status info
      try {
        const onboardingStatus = OnboardingJobTracker.getSummary(locationId);
        const docJobs = onboardingStatus.jobs.filter(
          (j: OnboardingJob) =>
            j.jobType === "document_vectorization" ||
            j.jobType.startsWith("document_upload_")
        );

        if (docJobs.length > 0) {
          // If we have job information, use it to complement our status
          const processingJobs = docJobs.filter(
            (j: OnboardingJob) => j.status === "processing"
          );

          if (processingJobs.length > 0 && status !== "completed") {
            status = "in_progress";
            inProgressCount = Math.max(inProgressCount, processingJobs.length);
          }
        }
      } catch (trackerError) {
        logger.warn(`Could not get job tracker info: ${trackerError}`);
        // Continue without this additional information
      }

      return {
        total_documents: documents.length,
        indexed_documents: indexedCount,
        failed_documents: failedCount,
        in_progress_documents: inProgressCount,
        status,
        last_updated:
          lastUpdated !== new Date(0).toISOString()
            ? lastUpdated
            : new Date().toISOString(),
      };
    } catch (error) {
      logger.error({
        message: "Error getting document indexing status",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        location_id: locationId,
      });

      // Return a default error state
      return {
        total_documents: 0,
        indexed_documents: 0,
        failed_documents: 0,
        in_progress_documents: 0,
        status: "failed",
        last_updated: new Date().toISOString(),
      };
    }
  }

  /**
   * Convert a location ID to a Pinecone namespace
   */
  private static getNamespace(locationId: number): string {
    return `location-${locationId}`;
  }

  /**
   * Get MIME type from file name
   */
  private static getMimeTypeFromFileName(fileName: string): string {
    const extension = path.extname(fileName).toLowerCase();

    const mimeTypes: Record<string, string> = {
      ".pdf": "application/pdf",
      ".doc": "application/msword",
      ".docx":
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ".txt": "text/plain",
      ".md": "text/markdown",
      ".csv": "text/csv",
      ".xls": "application/vnd.ms-excel",
      ".xlsx":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    };

    return mimeTypes[extension] || "application/octet-stream";
  }

  /**
   * Ensures that the vector index exists and creates it if needed
   */
  static async ensureIndexExists(): Promise<boolean> {
    try {
      await this.getVectorService().initialize();

      // Check if index exists first by listing indices
      const indices = await this.getVectorService().listIndices();
      const indexExists = indices.includes(DOCUMENT_INDEX);

      if (!indexExists) {
        logger.info(
          `Document index '${DOCUMENT_INDEX}' not found, creating it`
        );

        // Create index with 3072 dimensions for text-embedding-3-large
        await this.getVectorService().createIndex(DOCUMENT_INDEX, {
          dimension: 3072,
          metric: "cosine",
          serverless: {
            cloud: "aws",
            region: "us-east-1",
          },
        });

        logger.info(`Document index '${DOCUMENT_INDEX}' created successfully`);
      } else {
        logger.info(`Document index '${DOCUMENT_INDEX}' already exists`);
      }

      return true;
    } catch (error) {
      logger.error(`Failed to verify document index: ${error}`);
      return false;
    }
  }
}
