/**
 * @swagger
 * tags:
 *   name: Location
 *   description: Location management endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Location:
 *       type: object
 *       required: [id, name, locale, timezone]
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         description:
 *           type: string
 *           nullable: true
 *         website:
 *           type: string
 *           nullable: true
 *         facebook:
 *           type: string
 *           nullable: true
 *         twitter:
 *           type: string
 *           nullable: true
 *         instagram:
 *           type: string
 *           nullable: true
 *         linkedin:
 *           type: string
 *           nullable: true
 *         locale:
 *           type: string
 *         timezone:
 *           type: string
 *         text_opt_out_message:
 *           type: string
 *           nullable: true
 *         text_help_message:
 *           type: string
 *           nullable: true
 *         link_wrap_email:
 *           type: boolean
 *           nullable: true
 *         link_wrap_push:
 *           type: boolean
 *           nullable: true
 *         sender_email:
 *           type: string
 *           nullable: true
 *         sender_name:
 *           type: string
 *           nullable: true
 *         address:
 *           type: string
 *           nullable: true
 *         city:
 *           type: string
 *           nullable: true
 *         state:
 *           type: string
 *           nullable: true
 *         zip:
 *           type: string
 *           nullable: true
 *         phone:
 *           type: string
 *           nullable: true
 *         country:
 *           type: string
 *           nullable: true
 *         longitude:
 *           type: number
 *           nullable: true
 *         latitude:
 *           type: number
 *           nullable: true
 *         retailer_id:
 *           type: string
 *           nullable: true
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 */

/* eslint-disable indent */
import Router from "@koa/router";
import Location, { LocationParams } from "./Location";

import { JSONSchemaType, validate } from "../core/validate";
import { extractQueryParams } from "../utilities";
import { searchParamsSchema } from "../core/searchParams";
import Koa, { ParameterizedContext } from "koa";
import {
  allLocations,
  createLocation,
  getLocation,
  pagedLocations,
  requireLocationRole,
  updateLocation,
} from "./LocationService";
import {
  AuthState,
  LocationState,
  authMiddleware,
} from "../auth/AuthMiddleware";
import { getLocationAdmin } from "./LocationAdminRepository";
import { RequestError } from "../core/errors";
import { LocationError } from "./LocationError";
import { LocationRulePath } from "../rules/LocationRulePath";
import { getAdmin } from "../auth/AdminRepository";
import UserSchemaSyncJob from "../schema/UserSchemaSyncJob";
import App from "../app";
import { hasProvider } from "../providers/ProviderService";
import {
  requireOrganizationRole,
  markInviteCodeAsUsed,
} from "../organizations/OrganizationService";
import { createProvider } from "../providers/ProviderRepository";
import { uploadDocument } from "../documents/DocumentService";
import parseMultipartForm from "../storage/MultiFileParser";
import { logger } from "../config/logger";
import { OnboardingJobTracker } from "./OnboardingJobTracker";
import { importFromFile, importFromPOS } from "../pos/PosDataImportService";
import { canCreateLocation } from "../subscriptions/SubscriptionService";
import {
  importProducts,
  importFromMarketplace,
} from "../products/ProductImport";
import { Product } from "../products/Product";
import { SupabaseService } from "../supabase/SupabaseService";
import { ProductDataVectorService } from "../products/ProductDataVectorService";
import { LocationCompetitorService } from "./LocationCompetitorService";

type LocationSchemaType = {
  name: string;
  description?: string | null;
  locale: string;
  timezone: string;
  text_opt_out_message?: string | null;
  text_help_message?: string | null;
  link_wrap_email?: boolean | null;
  link_wrap_push?: boolean | null;
  data?: Record<string, any> | null;
};

export async function locationMiddleware(
  ctx: ParameterizedContext<LocationState>,
  next: () => void
) {
  if (ctx.state.scope !== "admin" && !ctx.state.key) {
    throw new RequestError(LocationError.LocationDoesNotExist);
  }

  if (ctx.state.scope === "admin" && !ctx.params.location) {
    throw new RequestError(LocationError.LocationDoesNotExist);
  }

  let locationId: number;

  if (ctx.state.scope === "admin") {
    locationId = ctx.params.location;
  } else {
    locationId = ctx.state.key!.location_id;
  }

  const location = await getLocation(locationId);
  if (!location) {
    throw new RequestError(LocationError.LocationDoesNotExist);
  }

  ctx.state.location = location;

  if (ctx.state.scope === "admin") {
    // admins and owners automatically get full access
    if (
      location.organization_id === ctx.state.admin!.organization_id &&
      ctx.state.admin?.role !== "member"
    ) {
      ctx.state.locationRole = "admin";
    } else {
      const locationAdmin = await getLocationAdmin(
        location.id,
        ctx.state.admin!.id
      );
      if (!locationAdmin) {
        throw new RequestError(LocationError.LocationAccessDenied);
      }
      ctx.state.locationRole = locationAdmin.role ?? "support";
    }
  } else {
    ctx.state.locationRole = ctx.state.key!.role ?? "support";
  }
  return next();
}

const router = new Router<AuthState>({ prefix: "/locations" });

/**
 * @swagger
 * /locations:
 *   get:
 *     summary: List Locations
 *     description: Retrieves a paginated list of locations for the current admin
 *     tags: [Location]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Location'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  const { id, organization_id } = ctx.state.admin!;
  ctx.body = await pagedLocations(params, id, organization_id);
});

/**
 * @swagger
 * /locations/all:
 *   get:
 *     summary: List All Locations
 *     description: Retrieves all locations for the current admin without pagination
 *     tags: [Location]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Location'
 */
router.get("/all", async (ctx) => {
  const { id, organization_id } = ctx.state.admin!;
  ctx.body = await allLocations(id, organization_id);
});

export const locationCreateParams: JSONSchemaType<LocationParams> = {
  $id: "locationCreate",
  type: "object",
  required: ["name", "locale", "timezone"],
  properties: {
    name: { type: "string" },
    description: { type: "string", nullable: true },
    website: { type: "string", nullable: true },
    facebook: { type: "string", nullable: true },
    twitter: { type: "string", nullable: true },
    instagram: { type: "string", nullable: true },
    linkedin: { type: "string", nullable: true },
    locale: { type: "string" },
    timezone: { type: "string" },
    text_opt_out_message: { type: "string", nullable: true },
    text_help_message: { type: "string", nullable: true },
    link_wrap_email: { type: "boolean", nullable: true },
    link_wrap_push: { type: "boolean", nullable: true },
    sender_email: { type: "string", nullable: true },
    sender_name: { type: "string", nullable: true },
    address: { type: "string", nullable: true },
    city: { type: "string", nullable: true },
    state: { type: "string", nullable: true },
    zip: { type: "string", nullable: true },
    phone: { type: "string", nullable: true },
    country: { type: "string", nullable: true },
    longitude: { type: "number", nullable: true },
    latitude: { type: "number", nullable: true },
    retailer_id: { type: "string", nullable: true },
    dataSources: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
    competitors: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
    documents: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
    socialPlatforms: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
    communicationSettings: {
      type: "object",
      nullable: true,
      required: [],
      additionalProperties: true,
    },
    data: {
      type: "object",
      nullable: true,
      required: [],
      additionalProperties: true,
    },
    social_media_accounts: {
      type: "object",
      nullable: true,
      required: [],
      additionalProperties: true,
    },
    pos_provider: {
      type: "string",
      nullable: true,
    },
    pos_configs: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /locations:
 *   post:
 *     summary: Create Location
 *     description: Creates a new location in the current organization
 *     tags: [Location]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LocationParams'
 *     responses:
 *       200:
 *         description: Location created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Location'
 *       400:
 *         description: Invalid input
 *       403:
 *         description: Subscription limit reached or admin not found
 */
router.post("/", async (ctx) => {
  requireOrganizationRole(ctx.state.admin!, "admin");
  const payload = validate(locationCreateParams, ctx.request.body);
  const { id, organization_id } = ctx.state.admin!;

  // Check if the organization has available subscriptions to create a new location
  const canCreate = await canCreateLocation(organization_id);
  if (!canCreate) {
    throw new RequestError({
      message:
        "Subscription limit reached. Please purchase additional subscriptions to create more locations.",
      code: 403,
      statusCode: 403,
    });
  }

  const admin = await getAdmin(id, organization_id);

  if (!admin) {
    throw new RequestError({
      message: "Admin not found or does not have access to this organization",
      code: 403,
      statusCode: 403,
    });
  }

  const location = await createLocation(admin, payload);
  ctx.body = location;

  await createProvider(Number(location.id), {
    data: {
      auth_token: process.env.TWILIO_AUTH_TOKEN,
      account_sid: process.env.TWILIO_ACCOUNT_SID,
      phone_number: process.env.TWILIO_PHONE_NUMBER,
    },
    name: "Twilio",
    is_default: true,
    rate_limit: 12,
    rate_interval: "second",
    type: "twilio",
    group: "text",
  });
  await createProvider(location.id, {
    data: {
      api_key: process.env.SENDGRID_API_KEY,
    },
    name: "Sendgrid",
    is_default: false,
    rate_limit: 12,
    rate_interval: "second",
    type: "sendgrid",
    group: "email",
  });
});

// Add a new endpoint for unified onboarding process
router.post("/onboard", async (ctx) => {
  console.log("==== ONBOARDING REQUEST RECEIVED ====");
  console.log("Headers:", JSON.stringify(ctx.request.headers));
  console.log("Content type:", ctx.request.headers["content-type"]);

  requireOrganizationRole(ctx.state.admin!, "admin");
  logger.info(
    `Starting location onboarding process for admin ${ctx.state.admin!.id}`
  );

  try {
    // Check if the organization has available subscriptions to create a new location
    const canCreate = await canCreateLocation(ctx.state.admin!.organization_id);
    if (!canCreate) {
      logger.error(
        `Organization ${
          ctx.state.admin!.organization_id
        } has reached subscription limit`
      );
      throw new RequestError({
        message:
          "Subscription limit reached. Please purchase additional subscriptions to create more locations.",
        code: 403,
        statusCode: 403,
      });
    }

    // Parse multipart form data using our new utility
    console.log("Attempting to parse multipart form data...");
    logger.info(`Parsing multipart form data for onboarding`);
    const formData = await parseMultipartForm(ctx);

    console.log("Form data parsed successfully!");
    console.log("Fields:", Object.keys(formData.fields));
    console.log("Files:", Object.keys(formData.files));

    logger.info(
      `Received form data with ${
        Object.keys(formData.fields).length
      } fields and ${Object.keys(formData.files).length} files`
    );

    if (Object.keys(formData.files).length > 0) {
      logger.info(
        `Files received: ${Object.keys(formData.files)
          .map((key) => `${key} (${formData.files[key].metadata.fileName})`)
          .join(", ")}`
      );
    }

    // Extract location data from the request
    const locationDataStr = formData.fields.locationData;
    if (!locationDataStr) {
      logger.error("Missing required location data in form fields");
      throw new RequestError({
        message: "Missing required location data",
        code: 400,
        statusCode: 400,
      });
    }

    // Parse location data
    logger.debug(
      `Parsing location data: ${locationDataStr.substring(0, 100)}...`
    );
    const locationData = JSON.parse(locationDataStr);

    // Check for invite code in the form data
    const inviteCode = formData.fields.inviteCode;
    logger.info(`Invite code from form data: '${inviteCode || "none"}'`);

    // Log if retailer_id is provided
    if (locationData.retailer_id) {
      logger.info(`Retailer ID provided: ${locationData.retailer_id}`);
    }

    logger.info(`Creating new location with name: ${locationData.name}`);

    // Remove competitors from main payload - will be processed separately
    const competitors = locationData.competitors;
    delete locationData.competitors;

    // Create the location first
    const admin = await getAdmin(
      ctx.state.admin!.id,
      ctx.state.admin!.organization_id
    );
    if (!admin) {
      logger.error(
        `Admin ${
          ctx.state.admin!.id
        } not found or does not have access to organization ${
          ctx.state.admin!.organization_id
        }`
      );
      throw new RequestError({
        message: "Admin not found or does not have access to this organization",
        code: 403,
        statusCode: 403,
      });
    }

    // Create the location
    const location = await createLocation(admin, locationData);
    logger.info(`Created location ${location.id} during onboarding`);

    // If this is the first location and an invite code was provided, mark it as used
    if (inviteCode) {
      try {
        const result = await markInviteCodeAsUsed(inviteCode, admin.email);
        if (result) {
          logger.info(
            `Successfully marked invite code ${inviteCode} as used by ${admin.email}`
          );
        } else {
          logger.warn(
            `Failed to mark invite code ${inviteCode} as used (may be invalid or already used)`
          );
        }
      } catch (error) {
        logger.error(`Error marking invite code as used: ${error}`);
        // Don't fail the onboarding process if marking the invite code fails
      }
    }

    // Set up initial providers
    logger.info(
      `Setting up initial communication providers for location ${location.id}`
    );
    await createProvider(Number(location.id), {
      data: {
        auth_token: process.env.TWILIO_AUTH_TOKEN,
        account_sid: process.env.TWILIO_ACCOUNT_SID,
        phone_number: process.env.TWILIO_PHONE_NUMBER,
      },
      name: "Twilio",
      is_default: true,
      rate_limit: 12,
      rate_interval: "second",
      type: "twilio",
      group: "text",
    });
    await createProvider(location.id, {
      data: {
        api_key: process.env.SENDGRID_API_KEY,
      },
      name: "Sendgrid",
      is_default: false,
      rate_limit: 12,
      rate_interval: "second",
      type: "sendgrid",
      group: "email",
    });
    logger.info(`Providers created successfully for location ${location.id}`);

    // Return immediate success response with the location data
    logger.info(
      `Returning immediate response for location ${location.id} while processing continues in background`
    );
    ctx.body = {
      success: true,
      message: "Location created, processing data in background",
      status: "processing",
      location,
    };

    // Process POS integration and file uploads in the background
    logger.info(`Starting background processing for location ${location.id}`);
    processOnboardingData(location.id, formData, competitors).catch((error) => {
      logger.error(
        `Background onboarding processing failed for location ${location.id}:`,
        error
      );
    });
  } catch (error: any) {
    logger.error("Onboarding process failed:", error);
    throw new RequestError({
      message: error.message || "Onboarding process failed",
      code: 400,
      statusCode: 400,
    });
  }
});

// Add the same endpoint for a specific location ID to support updates
router.put("/:location/onboard", async (ctx) => {
  requireOrganizationRole(ctx.state.admin!, "admin");
  const locationId = parseInt(ctx.params.location);
  logger.info(
    `Starting location update onboarding process for location ${locationId}, admin ${
      ctx.state.admin!.id
    }`
  );

  try {
    if (isNaN(locationId)) {
      logger.error(`Invalid location ID: ${ctx.params.location}`);
      throw new RequestError({
        message: "Invalid location ID",
        code: 400,
        statusCode: 400,
      });
    }

    // Parse multipart form data using our new utility
    logger.info(
      `Parsing multipart form data for location update ${locationId}`
    );
    const formData = await parseMultipartForm(ctx);
    logger.info(
      `Received form data with ${
        Object.keys(formData.fields).length
      } fields and ${Object.keys(formData.files).length} files`
    );

    if (Object.keys(formData.files).length > 0) {
      logger.info(
        `Files received: ${Object.keys(formData.files)
          .map((key) => `${key} (${formData.files[key].metadata.fileName})`)
          .join(", ")}`
      );
    }

    // Extract location data from the request
    const locationDataStr = formData.fields.locationData;
    if (!locationDataStr) {
      logger.error("Missing required location data in form fields");
      throw new RequestError({
        message: "Missing required location data",
        code: 400,
        statusCode: 400,
      });
    }

    // Parse location data
    logger.debug(
      `Parsing location data: ${locationDataStr.substring(0, 100)}...`
    );
    const locationData = JSON.parse(locationDataStr);
    logger.info(
      `Updating location ${locationId} with name: ${locationData.name}`
    );

    // Remove competitors from main payload - will be processed separately
    const competitors = locationData.competitors;
    delete locationData.competitors;

    // Get admin
    const admin = await getAdmin(
      ctx.state.admin!.id,
      ctx.state.admin!.organization_id
    );
    if (!admin) {
      logger.error(
        `Admin ${
          ctx.state.admin!.id
        } not found or does not have access to organization ${
          ctx.state.admin!.organization_id
        }`
      );
      throw new RequestError({
        message: "Admin not found or does not have access to this organization",
        code: 403,
        statusCode: 403,
      });
    }

    // Update the location
    const location = await updateLocation(locationId, admin.id, locationData);
    logger.info(`Updated location ${locationId} during onboarding`);

    // Return immediate success response with the location data
    logger.info(
      `Returning immediate response for location ${locationId} while processing continues in background`
    );
    ctx.body = {
      success: true,
      message: "Location updated, processing data in background",
      status: "processing",
      location,
    };

    // Process POS integration and file uploads in the background
    logger.info(`Starting background processing for location ${locationId}`);
    processOnboardingData(locationId, formData, competitors).catch((error) => {
      logger.error(
        `Background onboarding processing failed for location ${locationId}:`,
        error
      );
    });
  } catch (error: any) {
    logger.error("Onboarding update process failed:", error);
    throw new RequestError({
      message: error.message || "Onboarding update process failed",
      code: 400,
      statusCode: 400,
    });
  }
});

// Helper function to process onboarding data in the background
async function processOnboardingData(
  locationId: number,
  formData: { fields: Record<string, string>; files: Record<string, any> },
  competitors?: any[]
) {
  try {
    logger.info(`Begin background processing for location ${locationId}`);

    // Process competitors data if provided separately
    if (competitors && Array.isArray(competitors) && competitors.length > 0) {
      try {
        const jobType = "competitors_import";
        OnboardingJobTracker.startJob(locationId, jobType);

        logger.info(
          `Processing ${competitors.length} competitors for location ${locationId}`
        );

        // Use LocationCompetitorService to properly handle competitor data
        const competitorService = new LocationCompetitorService(App.main.db);

        // Format competitors data to match the service expectation
        const formattedCompetitors = competitors.map((comp: any) => ({
          place_id: comp.place_id || `custom-${Date.now()}`,
          name: comp.name,
          address: comp.address || "N/A",
          location: {
            lat: comp.location?.lat || 0,
            lng: comp.location?.lng || 0,
          },
          distance: comp.distance || 0,
        }));

        // Let the service handle transaction and DB operations
        await competitorService.addCompetitors(
          locationId,
          formattedCompetitors
        );

        logger.info(
          `Successfully saved ${formattedCompetitors.length} competitors for location ${locationId}`
        );
        OnboardingJobTracker.completeJob(locationId, jobType);
      } catch (error: any) {
        logger.error(`Failed to process competitors: ${error.message}`, error);
        OnboardingJobTracker.failJob(locationId, "competitors_import", error);
        // Continue with onboarding even if competitors import fails
      }
    } else if (formData.fields.locationData) {
      // Check for competitors in the locationData as a fallback
      try {
        const locationData = JSON.parse(formData.fields.locationData);
        if (
          locationData.competitors &&
          Array.isArray(locationData.competitors) &&
          locationData.competitors.length > 0
        ) {
          const jobType = "competitors_import";
          OnboardingJobTracker.startJob(locationId, jobType);

          logger.info(
            `Processing ${locationData.competitors.length} competitors from locationData for location ${locationId}`
          );

          // Use LocationCompetitorService to properly handle competitor data
          const competitorService = new LocationCompetitorService(App.main.db);

          // Format competitors data to match the service expectation
          const formattedCompetitors = locationData.competitors.map(
            (comp: any) => ({
              place_id: comp.place_id || `custom-${Date.now()}`,
              name: comp.name,
              address: comp.address || "N/A",
              location: {
                lat: comp.location?.lat || 0,
                lng: comp.location?.lng || 0,
              },
              distance: comp.distance || 0,
            })
          );

          // Let the service handle transaction and DB operations
          await competitorService.addCompetitors(
            locationId,
            formattedCompetitors
          );

          logger.info(
            `Successfully saved ${formattedCompetitors.length} competitors from locationData for location ${locationId}`
          );
          OnboardingJobTracker.completeJob(locationId, jobType);
        } else {
          logger.info(`No competitors data found for location ${locationId}`);
        }
      } catch (error: any) {
        logger.error(
          `Failed to process competitors from locationData: ${error.message}`,
          error
        );
        OnboardingJobTracker.failJob(locationId, "competitors_import", error);
        // Continue with onboarding even if competitors import fails
      }
    } else {
      logger.info(`No competitors data found for location ${locationId}`);
    }

    // Check if location was selected from Supabase
    if (formData.fields.locationData) {
      try {
        const locationData = JSON.parse(formData.fields.locationData);
        // Check both top level and inside data object for the properties
        const isFromOurDatabase =
          locationData.isFromOurDatabase ||
          (locationData.data && locationData.data.isFromOurDatabase);
        const retailerId =
          locationData.retailer_id ||
          (locationData.data && locationData.data.retailer_id);

        if (isFromOurDatabase && retailerId) {
          logger.info(
            `Location ${locationId} was selected from our database with retailer_id: ${retailerId}`
          );
          const jobType = "supabase_product_import";
          OnboardingJobTracker.startJob(locationId, jobType);

          // Initialize Supabase service
          const supabaseService = new SupabaseService({
            url: process.env.SUPABASE_URL || "",
            key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
            bucket: process.env.SUPABASE_BUCKET || "location-data",
          });

          try {
            // Get retailer products from Supabase
            logger.info(
              `Fetching products for retailer ${retailerId} from Supabase`
            );
            const response = await supabaseService.getRetailerProducts(
              retailerId
            );
            const products = response.products || [];

            if (products.length > 0) {
              logger.info(
                `Found ${products.length} products for retailer ${retailerId} in Supabase`
              );

              // Use the new importFromMarketplace function
              try {
                await importFromMarketplace(locationId, retailerId, products, {
                  enhance_with_ai: false,
                  reindex: true,
                });

                logger.info(
                  `Successfully imported products from Supabase for location ${locationId}`
                );
                OnboardingJobTracker.completeJob(locationId, jobType);
              } catch (importError: unknown) {
                // If the import fails with AI enhancement, try without it
                const importErrorMsg =
                  importError instanceof Error
                    ? importError.message
                    : String(importError);

                logger.warn(
                  `Import with AI enhancement failed, retrying without: ${importErrorMsg}`
                );

                try {
                  await importFromMarketplace(
                    locationId,
                    retailerId,
                    products,
                    {
                      enhance_with_ai: false,
                      reindex: true,
                    }
                  );

                  logger.info(
                    `Successfully imported products without AI enhancement for location ${locationId}`
                  );
                  OnboardingJobTracker.completeJob(locationId, jobType);
                } catch (finalError: unknown) {
                  const finalErrorMsg =
                    finalError instanceof Error
                      ? finalError.message
                      : String(finalError);

                  logger.error(
                    `All product import attempts failed: ${finalErrorMsg}`,
                    finalError instanceof Error
                      ? finalError
                      : new Error(String(finalError))
                  );
                  OnboardingJobTracker.failJob(
                    locationId,
                    jobType,
                    finalError instanceof Error
                      ? finalError
                      : new Error(String(finalError))
                  );
                }
              }
            } else {
              logger.info(
                `No products found for retailer ${retailerId} in Supabase`
              );
              OnboardingJobTracker.completeJob(locationId, jobType);
            }
          } catch (error: unknown) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            logger.error(
              `Failed to import products from Supabase: ${errorMessage}`,
              error instanceof Error ? error : new Error(errorMessage)
            );
            OnboardingJobTracker.failJob(
              locationId,
              jobType,
              error instanceof Error ? error : new Error(errorMessage)
            );
            // Continue with onboarding even if product import fails
          }
        }
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        logger.error(`Error parsing location data: ${errorMessage}`);
        // Continue with onboarding if we can't parse location data
      }
    }

    // Process POS integration if provided
    const posIntegrationStr = formData.fields.posIntegration;
    if (posIntegrationStr) {
      try {
        const jobType = "pos_integration";
        OnboardingJobTracker.startJob(locationId, jobType);

        logger.info(`Processing POS integration for location ${locationId}`);
        const posIntegration = JSON.parse(posIntegrationStr);
        logger.debug(`POS integration type: ${posIntegration.type}`);

        // Save POS provider information to location record
        try {
          await updateLocation(locationId, 0, {
            pos_provider: posIntegration.type,
            pos_configs: [
              {
                type: posIntegration.type,
                config: posIntegration.config,
                created_at: new Date().toISOString(),
                is_active: true,
              },
            ],
          });
          logger.info(
            `Successfully saved POS provider info for location ${locationId}: ${posIntegration.type}`
          );
        } catch (saveError: any) {
          logger.error(
            `Failed to save POS provider info for location ${locationId}: ${saveError.message}`,
            saveError
          );
        }

        try {
          await importFromPOS(locationId, {
            type: posIntegration.type,
            config: posIntegration.config,
          });
          logger.info(
            `Successfully configured POS integration for location ${locationId}`
          );

          OnboardingJobTracker.completeJob(locationId, jobType);
        } catch (posImportError: unknown) {
          // Log detailed error but keep going with onboarding
          const posErrorMsg =
            posImportError instanceof Error
              ? posImportError.message
              : String(posImportError);

          logger.error(
            `Failed to import from POS system: ${posErrorMsg}`,
            posImportError instanceof Error
              ? posImportError
              : new Error(String(posImportError))
          );
          OnboardingJobTracker.failJob(
            locationId,
            jobType,
            posImportError instanceof Error
              ? posImportError
              : new Error(String(posImportError))
          );
          // We'll still continue with onboarding
        }
      } catch (error: any) {
        logger.error(
          `Failed to configure POS integration: ${error.message}`,
          error
        );
        OnboardingJobTracker.failJob(locationId, "pos_integration", error);
        // Continue with onboarding even if POS setup fails
      }
    } else {
      logger.info(
        `No POS integration data provided for location ${locationId}`
      );
    }

    // Also check for POS integration data in locationData (from UI form)
    if (formData.fields.locationData) {
      try {
        const locationData = JSON.parse(formData.fields.locationData);
        const posIntegrations = locationData.data?.posIntegrations || [];

        if (posIntegrations.length > 0) {
          // Find the connected POS system
          const connectedPOS = posIntegrations.find(
            (pos: any) => pos.connected
          );

          if (connectedPOS) {
            logger.info(
              `Found connected POS system in location data: ${connectedPOS.id} for location ${locationId}`
            );

            // Prepare the config object based on POS type
            let posConfig: any = {};

            if (connectedPOS.id === "greencheck") {
              posConfig = {
                crb_id: connectedPOS.crbId || connectedPOS.apiKey,
                service_provider_id: "f2b48e57-7114-432d-8f11-a0d74b8fb934",
              };
            } else if (connectedPOS.id === "dutchie") {
              posConfig = {
                api_key: connectedPOS.apiKey,
                consumer_key: connectedPOS.consumerKey || "",
              };
            } else {
              posConfig = {
                api_key: connectedPOS.apiKey,
                site_id: connectedPOS.siteId,
              };
            }

            // Save POS provider information to location record
            try {
              await updateLocation(locationId, 0, {
                pos_provider: connectedPOS.id,
                pos_configs: [
                  {
                    type: connectedPOS.id,
                    config: posConfig,
                    created_at: new Date().toISOString(),
                    is_active: true,
                  },
                ],
              } as any);
              logger.info(
                `Successfully saved POS provider info from location data for location ${locationId}: ${connectedPOS.id}`
              );
            } catch (saveError: any) {
              logger.error(
                `Failed to save POS provider info from location data for location ${locationId}: ${saveError.message}`,
                saveError
              );
            }
          }
        }
      } catch (parseError: any) {
        logger.error(
          `Failed to parse location data for POS integration: ${parseError.message}`,
          parseError
        );
      }
    }

    // Process POS data file uploads
    const posDataFiles = Object.entries(formData.files)
      .filter(([key]) => key.startsWith("posDataFile_"))
      .map(([_, file]) => file);

    logger.info(
      `Processing ${posDataFiles.length} POS data files for location ${locationId}`
    );

    for (const [index, fileStream] of posDataFiles.entries()) {
      try {
        const jobType = `pos_data_file_${index}`;
        OnboardingJobTracker.startJob(locationId, jobType);

        logger.info(
          `Processing POS data file: ${fileStream.metadata.fileName}`
        );
        await importFromFile(locationId, fileStream);
        logger.info(
          `Successfully imported POS data file ${fileStream.metadata.fileName} for location ${locationId}`
        );

        OnboardingJobTracker.completeJob(locationId, jobType);
      } catch (error: any) {
        logger.error(
          `Failed to import POS data file ${fileStream.metadata.fileName}: ${error.message}`,
          error
        );
        OnboardingJobTracker.failJob(
          locationId,
          `pos_data_file_${index}`,
          error
        );
        // Continue with onboarding even if some file imports fail
      }
    }

    // Process document uploads
    const documentFiles = Object.entries(formData.files)
      .filter(([key]) => key.startsWith("document_"))
      .map(([_, file]) => file);

    logger.info(
      `Processing ${documentFiles.length} document files for location ${locationId}`
    );

    for (const [index, fileStream] of documentFiles.entries()) {
      try {
        const jobType = `document_upload_${index}`;
        OnboardingJobTracker.startJob(locationId, jobType);

        logger.info(
          `Processing document file: ${fileStream.metadata.fileName}`
        );
        await uploadDocument(locationId, fileStream);
        logger.info(
          `Successfully uploaded document ${fileStream.metadata.fileName} for location ${locationId}`
        );

        OnboardingJobTracker.completeJob(locationId, jobType);
      } catch (error: any) {
        logger.error(
          `Failed to upload document ${fileStream.metadata.fileName}: ${error.message}`,
          error
        );
        OnboardingJobTracker.failJob(
          locationId,
          `document_upload_${index}`,
          error
        );
        // Continue with onboarding even if some document uploads fail
      }
    }

    logger.info(
      `Background processing completed successfully for location ${locationId}`
    );
  } catch (error: any) {
    logger.error(
      `Error in background processing for location ${locationId}:`,
      error
    );
    throw error;
  }
}

// Endpoint to check onboarding processing status
router.get("/:location/onboard-status", async (ctx) => {
  requireOrganizationRole(ctx.state.admin!, "member");

  // Add timeout for this request
  const timeoutPromise = new Promise((_resolve, reject) => {
    setTimeout(() => reject(new Error("Request timeout")), 10000); // 10 second timeout
  });

  try {
    await Promise.race([
      (async () => {
        const locationId = parseInt(ctx.params.location);
        if (isNaN(locationId)) {
          throw new RequestError({
            message: "Invalid location ID",
            code: 400,
            statusCode: 400,
          });
        }

        console.log(`Checking onboard status for location ${locationId}`);

        // Add timeout to database query
        const location = await Promise.race([
          getLocation(locationId),
          new Promise((_resolve, reject) =>
            setTimeout(() => reject(new Error("Database query timeout")), 5000)
          ),
        ]);

        if (!location || !(location instanceof Location)) {
          throw new RequestError({
            message: "Location not found",
            code: 404,
            statusCode: 404,
          });
        }

        // Check if there are any jobs being tracked by our OnboardingJobTracker
        const jobStatus = OnboardingJobTracker.getSummary(locationId);

        // Use a time-based fallback if no jobs are found but the location was recently created
        const recentLocation =
          location &&
          new Date().getTime() - new Date(location.created_at).getTime() <
            1000 * 60 * 15; // Less than 15 minutes old

        // If any job is processing, or the location was just created and there are no completed jobs,
        // consider it "processing"
        const isProcessing =
          jobStatus.processing > 0 || (recentLocation && jobStatus.total === 0);

        console.log(
          `Onboard status for location ${locationId}: processing=${isProcessing}`
        );

        // Build a response with detailed information
        ctx.body = {
          success: true,
          location_id: locationId,
          is_processing: isProcessing,
          processing_details: {
            recently_created: recentLocation,
            created_at: location.created_at,
            updated_at: location.updated_at,
            total_jobs: jobStatus.total,
            completed_jobs: jobStatus.completed,
            processing_jobs: jobStatus.processing,
            failed_jobs: jobStatus.failed,
            jobs: jobStatus.jobs,
          },
        };
      })(),
      timeoutPromise,
    ]);
  } catch (error: any) {
    console.error(
      `Error checking onboarding status for location ${ctx.params.location}:`,
      error
    );

    if (
      error.message === "Request timeout" ||
      error.message === "Database query timeout"
    ) {
      ctx.status = 504;
      ctx.body = {
        success: false,
        error: "Request timeout",
        message: "The onboarding status check took too long. Please try again.",
      };
    } else {
      logger.error(`Error checking onboarding status: ${error.message}`);
      throw new RequestError({
        message: error.message || "Failed to check onboarding status",
        code: error.statusCode || 400,
        statusCode: error.statusCode || 400,
      });
    }
  }
});

export default router;

const subrouter = new Router<LocationState>();

subrouter.get("/", async (ctx) => {
  ctx.body = {
    ...ctx.state.location,
    role: ctx.state.locationRole,
    has_provider: await hasProvider(ctx.state.location.id),
  };
});

export const locationUpdateParams: JSONSchemaType<Partial<LocationParams>> = {
  $id: "locationUpdate",
  type: "object",
  required: [],
  properties: {
    name: { type: "string", nullable: true },
    description: { type: "string", nullable: true },
    website: { type: "string", nullable: true },
    facebook: { type: "string", nullable: true },
    twitter: { type: "string", nullable: true },
    instagram: { type: "string", nullable: true },
    linkedin: { type: "string", nullable: true },
    locale: { type: "string", nullable: true },
    timezone: { type: "string", nullable: true },
    text_opt_out_message: { type: "string", nullable: true },
    text_help_message: { type: "string", nullable: true },
    link_wrap_email: { type: "boolean", nullable: true },
    link_wrap_push: { type: "boolean", nullable: true },
    sender_email: { type: "string", nullable: true },
    sender_name: { type: "string", nullable: true },
    address: { type: "string", nullable: true },
    city: { type: "string", nullable: true },
    state: { type: "string", nullable: true },
    zip: { type: "string", nullable: true },
    phone: { type: "string", nullable: true },
    country: { type: "string", nullable: true },
    longitude: { type: "number", nullable: true },
    latitude: { type: "number", nullable: true },
    retailer_id: { type: "string", nullable: true },
    dataSources: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
    competitors: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
    documents: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
    socialPlatforms: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
    communicationSettings: {
      type: "object",
      nullable: true,
      required: [],
      additionalProperties: true,
    },
    data: {
      type: "object",
      nullable: true,
      required: [],
      additionalProperties: true,
    },
    social_media_accounts: {
      type: "object",
      nullable: true,
      required: [],
      additionalProperties: true,
    },
    pos_provider: {
      type: "string",
      nullable: true,
    },
    pos_configs: {
      type: "array",
      nullable: true,
      items: {
        type: "object",
        required: [],
        additionalProperties: true,
      },
    },
  },
  additionalProperties: false,
};

subrouter.patch("/", async (ctx) => {
  requireLocationRole(ctx, "admin");
  const { admin, location } = ctx.state;
  const payload = validate(locationUpdateParams, ctx.request.body);
  ctx.body = await updateLocation(location.id, admin!.id, payload);
});

subrouter.get("/data/paths", async (ctx) => {
  ctx.body = await LocationRulePath.all((q: any) =>
    q.where("location_id", ctx.state.location.id)
  ).then((list: any) =>
    list.reduce(
      (a: any, { type, name, path }: any) => {
        if (type === "event") {
          (a.eventPaths[name!] ?? (a.eventPaths[name!] = [])).push(path);
        } else {
          a.userPaths.push(path);
        }
        return a;
      },
      {
        userPaths: [],
        eventPaths: {},
      } as {
        userPaths: string[];
        eventPaths: { [name: string]: string[] };
      }
    )
  );
});

subrouter.post("/data/paths/sync", async (ctx) => {
  App.main.queue.enqueue(
    UserSchemaSyncJob.from({
      location_id: ctx.state.location.id,
      // no delta, rebuild the whole thing
    })
  );
  ctx.status = 204;
});

// Document vector management endpoints
subrouter.post("/document-vectors/reindex-all", async (ctx) => {
  requireLocationRole(ctx, "admin");
  const { location } = ctx.state;

  try {
    // Import dynamically to avoid circular dependencies
    const { DocumentVectorService } = await import(
      "../documents/DocumentVectorService"
    );

    // Start a background process to reindex all documents
    const result = await DocumentVectorService.reindexLocationDocuments(
      location.id
    );

    ctx.body = {
      success: true,
      message: "Document reindexing started",
      job_id: result.jobId,
      documents: result.documentCount,
    };
  } catch (error: any) {
    logger.error(`Error starting document reindexing: ${error.message}`, error);
    throw new RequestError(
      `Failed to start document reindexing: ${error.message}`
    );
  }
});

subrouter.get("/document-vectors/status", async (ctx) => {
  const { location } = ctx.state;

  try {
    // Import dynamically to avoid circular dependencies
    const { DocumentVectorService } = await import(
      "../documents/DocumentVectorService"
    );

    // Get indexing status for this location
    const status = await DocumentVectorService.getLocationIndexingStatus(
      location.id
    );

    ctx.body = {
      success: true,
      status,
    };
  } catch (error: any) {
    logger.error(
      `Error getting document vector status: ${error.message}`,
      error
    );
    throw new RequestError(
      `Failed to get document vector status: ${error.message}`
    );
  }
});

export { subrouter as LocationSubrouter };

/**
 * @swagger
 * /locations/{locationId}/import/products:
 *   post:
 *     summary: Import products for a location
 *     description: Imports products from various sources for a location
 *     tags: [Location]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [source]
 *             properties:
 *               source:
 *                 type: string
 *                 enum: [pos, marketplace, file]
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Required if source is 'file'
 *     responses:
 *       200:
 *         description: Products imported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *       400:
 *         description: Invalid request body
 *       404:
 *         description: Location not found
 */
router.post("/:locationId/import/products", async (ctx) => {
  // ... existing code ...
});

/**
 * @swagger
 * /locations/{locationId}/import/pos:
 *   post:
 *     summary: Import POS data for a location
 *     description: Imports point of sale data for a location
 *     tags: [Location]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: POS data file
 *     responses:
 *       200:
 *         description: POS data imported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *       400:
 *         description: Invalid request body
 *       404:
 *         description: Location not found
 */
router.post("/:locationId/import/pos", async (ctx) => {
  // ... existing code ...
});

/**
 * @swagger
 * /locations/{locationId}/documents:
 *   post:
 *     summary: Upload documents for a location
 *     description: Uploads and processes documents for a location
 *     tags: [Location]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Document files to upload
 *     responses:
 *       200:
 *         description: Documents uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *       400:
 *         description: Invalid request body
 *       404:
 *         description: Location not found
 */
router.post("/:locationId/documents", async (ctx) => {
  // ... existing code ...
});
